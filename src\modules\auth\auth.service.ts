import { validateHash } from '@common/utils';
import { RoleType } from '@constants/role-type';
import { TokenType } from '@constants/token-type';
import { UserAccountStatus, UserAccountType } from '@constants/user';
import { UserService } from '@modules/user/user.service';
import type { UserAccountEntity } from '@modules/user/user-account.entity';
import {
  ConflictException,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ApiConfigService } from '@shared/services/api-config.service';

import { UserNotFoundException } from '../../exceptions/user-not-found.exception.ts';
import type { SocialInfoDto } from './dto/social-info.dto.ts';
import { TokenPayloadDto } from './dto/token-payload.dto.ts';
import type { UserLoginDto } from './dto/user-login.dto.ts';

@Injectable()
export class AuthService {
  constructor(
    private jwtService: JwtService,
    private configService: ApiConfigService,
    private userService: UserService,
  ) {}

  async createToken(data: {
    role: RoleType;
    userId: number;
  }): Promise<TokenPayloadDto> {
    return new TokenPayloadDto({
      userId: data.userId,
      expiresIn: this.configService.authConfig.jwtExpirationTime,
      token: await this.jwtService.signAsync({
        userId: data.userId,
        type: TokenType.ACCESS_TOKEN,
        role: data.role,
      }),
      refreshToken: await this.jwtService.signAsync({
        userId: data.userId,
        type: TokenType.REFRESH_TOKEN,
        role: data.role,
      }),
    });
  }

  async validateUser(userLoginDto: UserLoginDto): Promise<UserAccountEntity> {
    // if (!userLoginDto.username && !userLoginDto.email) {
    //   throw new UserNotFoundException();
    // }

    const user = await this.userService.findByUsernameOrEmail({
      username: userLoginDto.username,
      email: null,
    });

    // const user = await this.userService.findOne({
    //   email: userLoginDto.email,
    // });

    if (user?.status !== UserAccountStatus.ACTIVE) {
      throw new UserNotFoundException();
    }

    const isPasswordValid = await validateHash(
      userLoginDto.password,
      user?.passwordHash,
    );

    if (!isPasswordValid) {
      throw new UserNotFoundException();
    }

    return user;
  }

  async getUserFromToken(token: string): Promise<UserAccountEntity> {
    const payload = await this.jwtService.verifyAsync(token, {
      secret: this.configService.authConfig.publicKey,
    });

    if (payload.type !== TokenType.ACCESS_TOKEN) {
      throw new UserNotFoundException();
    }

    const user = await this.userService.findOne({
      userId: payload.userId,
      // role: RoleType.USER,
    });

    if (!user) {
      throw new UserNotFoundException();
    }

    return user;
  }

  async getUserFromRefreshToken(token: string): Promise<UserAccountEntity> {
    const payload = await this.jwtService.verifyAsync(token, {
      secret: this.configService.authConfig.publicKey,
    });

    if (payload.type !== TokenType.REFRESH_TOKEN) {
      throw new UserNotFoundException();
    }

    const user = await this.userService.findOne({
      userId: payload.userId,
      // role: RoleType.USER,
    });

    if (!user) {
      throw new UserNotFoundException();
    }

    if (!user.refreshToken || user.refreshToken !== token) {
      throw new UserNotFoundException();
    }

    return user;
  }

  async loginWithOAuth(
    socialInfo: SocialInfoDto,
    ip: string,
  ): Promise<{ token?: string | null; status: UserAccountStatus }> {
    const { email, socialUid, provider } = socialInfo;

    let matchedUser: UserAccountEntity | null = null;

    // Step 1: Tìm theo socialUid
    const userBySocialUid = await this.userService.findOne({
      socialUid,
      accountType: provider,
    });

    if (userBySocialUid) {
      matchedUser = userBySocialUid;
    } else if (email) {
      // Step 2: Tìm theo email (có thể là local account)
      const userByEmail = await this.userService.findOne({ email });

      if (userByEmail) {
        if (userByEmail.accountType === UserAccountType.QUICKPLAY) {
          // ❌ Quickplay không được liên kết SSO
          throw new ForbiddenException(
            'Không thể liên kết tài khoản Quickplay. Vui lòng nâng cấp lên tài khoản cục bộ trước.',
          );
        }

        if (userByEmail.socialUid) {
          // ❌ Đã liên kết với social khác → từ chối
          throw new ConflictException(
            'Tài khoản này đã được liên kết với một nhà cung cấp mạng xã hội khác.',
          );
        }

        // ✅ Cho phép liên kết SSO
        matchedUser = await this.userService.linkUserSSO(
          userByEmail,
          socialInfo,
        );
      }
    }

    // Step 3: Nếu không có user nào thì tạo mới
    if (!matchedUser) {
      matchedUser = await this.userService.createUserSSO(socialInfo, ip);
    }

    // Step 4: Check trạng thái
    if (matchedUser.status !== UserAccountStatus.ACTIVE) {
      return {
        token: null,
        status: matchedUser.status as UserAccountStatus,
      };
    }

    // Step 5: Cấp token
    const token = await this.createToken({
      userId: matchedUser.userId,
      role: RoleType.USER,
    });

    // Step 6: Cập nhật login info
    await this.userService.updateLastLoginInfo(matchedUser.userId, ip);
    await this.userService.updateRefreshToken(
      matchedUser.userId,
      token.refreshToken,
    );

    return {
      token: token.token,
      status: matchedUser.status,
    };
  }
}
