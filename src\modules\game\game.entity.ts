import { AbstractEntity } from '@common/abstract.entity';
import { UseDto } from '@decorators/use-dto.decorator';
import {
  Column,
  Entity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { GameDto } from './dtos/game.dto.ts';

@Index('game_pkey', ['gameId'], { unique: true })
@Index('game_game_key_key', ['gameKey'], { unique: true })
@Entity('game', { schema: 'public' })
@UseDto(GameDto)
export class GameEntity extends AbstractEntity<GameDto> {
  @PrimaryGeneratedColumn({ type: 'integer', name: 'game_id' })
  gameId!: number;

  @Column('character varying', { name: 'game_key', unique: true, length: 50 })
  gameKey!: string;

  @Column('character varying', { name: 'game_name', length: 100 })
  gameName!: string;

  @Column('timestamp without time zone', {
    name: 'created_at',
    default: () => 'CURRENT_TIMESTAMP',
  })
  declare createdAt: Date;

  @Column('timestamp without time zone', {
    name: 'updated_at',
    default: () => 'CURRENT_TIMESTAMP',
  })
  declare updatedAt: Date;

  @OneToMany('AdminRoleMappingEntity', 'game')
  adminRoleMappings!: Array<'AdminRoleMappingEntity'>;

  @OneToMany('PaymentTransactionEntity', 'game')
  paymentTransactions!: Array<'PaymentTransactionEntity'>;

  @OneToMany('UserGameMappingEntity', 'game')
  userGameMappings!: Array<'UserGameMappingEntity'>;
}
