import { StringField } from '@decorators/field.decorators';
import { Expose } from 'class-transformer';

import { UserQuickplayLoginDto } from './user-quickplay-login.dto.ts';

export class UserQuickplayLinkDto extends UserQuickplayLoginDto {
  @StringField()
  password!: string;

  @StringField()
  @Expose({ name: 'qp_id' })
  qpId!: string;

  @StringField()
  @Expose({ name: 'qp_token' })
  qpToken!: string;

  constructor(partial: Partial<UserQuickplayLinkDto>) {
    super(partial);

    Object.assign(this, partial);
  }
}
