import { UserAccountType } from '@constants/user';
import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import type { Profile } from 'passport-facebook';
import { Strategy } from 'passport-facebook';

import { SocialInfoDto } from './dto/social-info.dto.ts';

@Injectable()
export class FacebookStrategy extends PassportStrategy(Strategy, 'facebook') {
  constructor() {
    super({
      clientID: process.env.FACEBOOK_CLIENT_ID!,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET!,
      callbackURL: `${process.env.BACKEND_URL}/auth/oauth2/facebook/callback`,
      scope: ['email'],
      profileFields: ['id', 'emails', 'name', 'picture.type(large)'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: Profile,
    done: Function,
  ) {
    const { id, name, emails, photos } = profile;
    const socialInfo = new SocialInfoDto({
      socialUid: id,
      name: `${name?.givenName || ''} ${name?.familyName || ''}`,
      email: emails?.[0]?.value,
      avatarUrl: photos?.[0]?.value,
      provider: UserAccountType.FACEBOOK,
      accessToken,
      refreshToken,
    });
    done(null, socialInfo);
  }
}
