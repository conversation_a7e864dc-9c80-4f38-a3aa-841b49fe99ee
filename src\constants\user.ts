export enum UserAccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned',
  PENDING_VERIFICATION = 'pending_verification',
}

export enum UserAccountType {
  QUICKPLAY = 'quickplay',
  LOCAL = 'local',
  FACEBOOK = 'facebook',
  GOOGLE = 'google',
  APPLE = 'apple',
}

export const SocialCode = new Map<UserAccountType, string>([
  [UserAccountType.FACEBOOK, 'fb'],
  [UserAccountType.GOOGLE, 'gg'],
  [UserAccountType.APPLE, 'ap'],
  [UserAccountType.LOCAL, 'lc'],
  [UserAccountType.QUICKPLAY, 'qp'],
]);
