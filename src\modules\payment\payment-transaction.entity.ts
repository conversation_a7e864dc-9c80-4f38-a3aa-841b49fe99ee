import { AbstractEntity } from '@common/abstract.entity';
import { PaymentStatus } from '@constants/payment';
import { UseDto } from '@decorators/use-dto.decorator';
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { PaymentTransactionDto } from './dtos/payment-transaction.dto.ts';

@Index('idx_payment_game', ['gameId'], {})
@Index('payment_transaction_order_id_key', ['orderId'], { unique: true })
@Index('idx_payment_status', ['status'], {})
@Index('payment_transaction_pkey', ['txId'], { unique: true })
@Index('idx_payment_user', ['userId'], {})
@Entity('payment_transaction', { schema: 'public' })
@UseDto(PaymentTransactionDto)
export class PaymentTransactionEntity extends AbstractEntity<PaymentTransactionDto> {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'tx_id' })
  txId!: number;

  @Column('bigint', { name: 'user_id' })
  userId!: number;

  @Column('integer', { name: 'game_id' })
  gameId!: number;

  @Column('character varying', { name: 'order_id', unique: true, length: 100 })
  orderId!: string;

  @Column('numeric', { name: 'amount', precision: 10, scale: 2 })
  amount!: number;

  @Column('character varying', { name: 'currency', length: 10 })
  currency!: string;

  @Column('character varying', { name: 'payment_method', length: 30 })
  paymentMethod!: string;

  @Column('character varying', {
    name: 'status',
    length: 30,
    default: PaymentStatus.PENDING,
  })
  status!: string;

  @Column('character varying', { name: 'note', length: 100 })
  note!: string;

  @Column('timestamp without time zone', {
    name: 'created_at',
    default: () => 'CURRENT_TIMESTAMP',
  })
  declare createdAt: Date;

  @Column('timestamp without time zone', {
    name: 'updated_at',
    default: () => 'CURRENT_TIMESTAMP',
  })
  declare updatedAt: Date;

  @ManyToOne('GameEntity', 'paymentTransactions')
  @JoinColumn([{ name: 'game_id', referencedColumnName: 'gameId' }])
  game!: 'GameEntity';

  @ManyToOne('UserAccountEntity', 'paymentTransactions')
  @JoinColumn([{ name: 'user_id', referencedColumnName: 'userId' }])
  user!: 'UserAccountEntity';
}
