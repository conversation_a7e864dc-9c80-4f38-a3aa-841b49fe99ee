import { AbstractDto } from '@common/dto/abstract.dto';
import {
  <PERSON><PERSON><PERSON>,
  NumberField,
  StringField,
} from '@decorators/field.decorators';

import type { AuditLogEntity } from '../audit-log.entity';

export class AuditLogDto extends AbstractDto {
  @NumberField()
  logId: number;

  @NumberField()
  userId?: number | null;

  @NumberField()
  adminId?: number | null;

  @StringField()
  actionType: string;

  @StringField()
  description: string | null;

  @StringField()
  ipAddress: string | null;

  @DateField()
  createdAt: Date;

  constructor(auditLog: AuditLogEntity) {
    super(auditLog);
    this.logId = auditLog.logId;
    this.userId = auditLog.userId;
    this.adminId = auditLog.adminId;
    this.actionType = auditLog.actionType;
    this.description = auditLog.description;
    this.ipAddress = auditLog.ipAddress;
    this.createdAt = auditLog.createdAt;
  }
}
