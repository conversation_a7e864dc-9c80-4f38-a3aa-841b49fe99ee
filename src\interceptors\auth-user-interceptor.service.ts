import type { UserAccountEntity } from '@modules/user/user-account.entity.ts';
import type {
  <PERSON><PERSON><PERSON>ler,
  ExecutionContext,
  NestInterceptor,
} from '@nestjs/common';
import { Injectable, UnauthorizedException } from '@nestjs/common';

import { ContextProvider } from '../providers/context.provider.ts';

@Injectable()
export class AuthUserInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler) {
    const request = context
      .switchToHttp()
      .getRequest<{ user: UserAccountEntity }>();

    const user = request.user;

    if (!user) {
      throw new UnauthorizedException();
    }

    ContextProvider.setAuthUser(user);

    return next.handle();
  }
}
