import { PaymentMethod, PaymentStatus } from '@constants/payment';
import {
  DateField,
  EnumField,
  NumberField,
  StringField,
} from '@decorators/field.decorators';


export class TransactionHistoryDto {
  @NumberField()
  txId: number;

  @NumberField()
  gameId: number;

  @StringField()
  orderId: string;

  @NumberField()
  amount: number;

  @StringField()
  currency: string;

  @EnumField(() => PaymentMethod)
  paymentMethod: PaymentMethod;

  @EnumField(() => PaymentStatus)
  status: PaymentStatus;

  @StringField()
  note: string;

  @DateField()
  createdAt: Date;

  constructor(partial: Partial<TransactionHistoryDto>) {
    Object.assign(this, partial);
  }
}
