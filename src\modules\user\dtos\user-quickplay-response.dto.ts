import { DateField, StringField } from '@decorators/field.decorators';
import { Expose } from 'class-transformer';

export class UserQuickplayResponseDto {
  @StringField()
  @Expose({ name: 'qp_id' })
  qpId!: string;

  @StringField()
  @Expose({ name: 'qp_token' })
  qpToken!: string;

  @DateField()
  @Expose({ name: 'created_at' })
  createdAt!: Date;

  constructor(partial: Partial<UserQuickplayResponseDto>) {
    Object.assign(this, partial);
  }
}
