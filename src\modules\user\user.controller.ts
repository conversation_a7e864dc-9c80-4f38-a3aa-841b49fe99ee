import { PageDto } from '@common/dto/page.dto';
import { ResponseDto } from '@common/dto/response.dto.ts';
import { getIp } from '@common/utils.ts';
import { RoleType } from '@constants/role-type';
import { ApiPageResponse } from '@decorators/api-page-response.decorator';
import { AuthUser } from '@decorators/auth-user.decorator';
import { Auth, UUIDParam } from '@decorators/http.decorators';
import { SocialInfoDto } from '@modules/auth/dto/social-info.dto.ts';
import { UserAccountDto } from '@modules/user/dtos/user-account.dto';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  ValidationPipe,
  Version,
} from '@nestjs/common';
import {
  ApiAcceptedResponse,
  ApiBody,
  ApiOkResponse,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { TranslationService } from '@shared/services/translation.service';
import type { Request } from 'express';

import { UseLanguageInterceptor } from '../../interceptors/language-interceptor.service.ts';
import { UpdateUserProfileDto } from './dtos/update-user-profile.dto.ts';
import type { UserQuickplayLinkDto } from './dtos/user-quickplay-link.dto.ts';
import type { UserQuickplayLoginDto } from './dtos/user-quickplay-login.dto.ts';
import { UserQuickplayResponseDto } from './dtos/user-quickplay-response.dto.ts';
import { UsersPageOptionsDto } from './dtos/users-page-options.dto.ts';
import { UserService } from './user.service.ts';
import type { UserAccountEntity } from './user-account.entity.ts';
import { TransactionHistoryOptionsDto } from '@modules/payment/dtos/transaction-history-options.dto.ts';
import { TransactionHistoryDto } from '@modules/payment/dtos/transaction-history.dto';

@Controller('user')
@ApiTags('user')
export class UserController {
  constructor(
    private userService: UserService,
    private readonly translationService: TranslationService,
  ) {}

  @Version('1')
  @Get('profile')
  @HttpCode(HttpStatus.OK)
  @Auth([RoleType.USER])
  @ApiOkResponse({ type: UserAccountDto, description: 'current user profile' })
  getUserProfile(@AuthUser() user: UserAccountEntity): UserAccountDto {
    return user.toDto();
  }

  @Version('1')
  @Put('profile')
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiAcceptedResponse()
  @Auth([RoleType.USER])
  @ApiBody({ type: UpdateUserProfileDto })
  @ApiOkResponse({ type: ResponseDto, description: 'update user profile' })
  async updateUserProfile(
    @AuthUser() user: UserAccountEntity,
    @Body() updateUserProfileDto: UpdateUserProfileDto,
  ): Promise<ResponseDto<null>> {
    return this.userService.updateUserProfile(user, updateUserProfileDto);
  }

  @Version('1')
  @Post('quickplay')
  @HttpCode(HttpStatus.OK)
  @Auth([RoleType.USER])
  @ApiOkResponse({
    type: UserQuickplayResponseDto,
    description: 'create quickplay user',
  })
  async createQuickplayUser(
    @Body() userQuickplayDto: UserQuickplayLoginDto,
    @Req() request: Request,
  ): Promise<UserQuickplayResponseDto> {
    if (userQuickplayDto.username) {
      const res = this.userService.loginUserQuickplay(
        userQuickplayDto,
        getIp(request),
      );

      if (res instanceof Promise) {
        return res;
      }
    }

    return this.userService.createUserQuickplay(
      userQuickplayDto,
      getIp(request),
    );
  }

  @Version('1')
  @Post('quickplay/link')
  @HttpCode(HttpStatus.OK)
  @Auth([RoleType.USER])
  @ApiOkResponse({
    type: UserQuickplayResponseDto,
    description: 'link quickplay user',
  })
  async linkUserQuickplay(
    @Body() userQuickplayDto: UserQuickplayLinkDto,
    @Req() request: Request,
  ): Promise<ResponseDto<null>> {
    return this.userService.linkUserQuickplay(userQuickplayDto, getIp(request));
  }

  @Version('1')
  @Get('social/account')
  @HttpCode(HttpStatus.OK)
  @Auth([RoleType.USER])
  @ApiOkResponse({
    type: SocialInfoDto,
    description: 'Get social account info',
  })
  async getSocialInfo(
    @AuthUser() user: UserAccountEntity,
  ): Promise<SocialInfoDto> {
    return this.userService.getSocialInfo(user);
  }

  @Version('1')
  @Delete('social/account/:provider')
  @HttpCode(HttpStatus.OK)
  @Auth([RoleType.USER])
  @ApiOkResponse({
    type: ResponseDto,
    description: 'Unlink social account',
  })
  async unlinkSocialAccount(
    @AuthUser() user: UserAccountEntity,
    @Param('provider') provider: string,
  ): Promise<ResponseDto<null>> {
    return this.userService.unlinkSocialAccount(user, provider);
  }

  @Version('1')
  @Get('transactions/history')
  @HttpCode(HttpStatus.OK)
  @Auth([RoleType.USER])
  @ApiPageResponse({
    type: PageDto<TransactionHistoryDto>,
    description: 'Get transaction history',
  })
  async getTransactionHistory(
    @AuthUser() user: UserAccountEntity,
    @Query(new ValidationPipe({ transform: true })) transactionHistoryOptionsDto: TransactionHistoryOptionsDto,
  ): Promise<PageDto<TransactionHistoryDto>> {
    return this.userService.getTransactionHistory(user, transactionHistoryOptionsDto);
  }

  @Get('admin')
  @Auth([RoleType.USER])
  @HttpCode(HttpStatus.OK)
  @UseLanguageInterceptor()
  async admin(@AuthUser() user: UserAccountEntity) {
    const translation = await this.translationService.translate(
      'admin.keywords.admin',
    );

    return {
      text: `${translation} ${user.userProfile.displayName}`,
    };
  }

  @Get()
  @Auth([RoleType.USER])
  @HttpCode(HttpStatus.OK)
  @ApiPageResponse({
    description: 'Get users list',
    type: PageDto,
  })
  getUsers(
    @Query(new ValidationPipe({ transform: true }))
    pageOptionsDto: UsersPageOptionsDto,
  ): Promise<PageDto<UserAccountDto>> {
    return this.userService.getUsers(pageOptionsDto);
  }

  @Get(':id')
  @Auth([RoleType.USER])
  @HttpCode(HttpStatus.OK)
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Get users list',
    type: UserAccountDto,
  })
  getUser(@UUIDParam('id') userId: number): Promise<UserAccountDto> {
    return this.userService.getUser(userId);
  }
}
