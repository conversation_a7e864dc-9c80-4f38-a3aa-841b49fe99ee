import type { ICommand, ICommandHandler } from '@nestjs/cqrs';
import { CommandHandler } from '@nestjs/cqrs';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import type { CreateUserProfileDto } from '../dtos/create-user-profile.dto.ts';
import { UserProfileEntity } from '../user-profile.entity.ts';

export class CreateUserProfileCommand implements ICommand {
  constructor(
    public readonly userId: number,
    public readonly createUserProfileDto: CreateUserProfileDto,
  ) {}
}

@CommandHandler(CreateUserProfileCommand)
export class CreateUserProfileHandler
  implements ICommandHandler<CreateUserProfileCommand, UserProfileEntity>
{
  constructor(
    @InjectRepository(UserProfileEntity)
    private userProfileRepository: Repository<UserProfileEntity>,
  ) {}

  execute(command: CreateUserProfileCommand) {
    const { userId, createUserProfileDto } = command;
    const userProfileEntity =
      this.userProfileRepository.create(createUserProfileDto);

    userProfileEntity.userId = userId;

    return this.userProfileRepository.save(userProfileEntity);
  }
}
