import { AbstractEntity } from '@common/abstract.entity';
import { UseDto } from '@decorators/use-dto.decorator';
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { AdminRoleMappingDto } from './dtos/admin-role-mapping.dto.ts';

@Index('idx_admin_role_mapping', ['adminId', 'gameId', 'roleId'], {})
@Index('admin_role_mapping_pkey', ['armId'], { unique: true })
@Entity('admin_role_mapping', { schema: 'public' })
@UseDto(AdminRoleMappingDto)
export class AdminRoleMappingEntity extends AbstractEntity<AdminRoleMappingDto> {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'arm_id' })
  armId!: string;

  @Column('bigint', { name: 'admin_id' })
  adminId!: string;

  @Column('smallint', { name: 'role_id' })
  roleId!: number;

  @Column('integer', { name: 'game_id', nullable: true })
  gameId!: number | null;

  @Column('timestamp without time zone', {
    name: 'assigned_at',
    default: () => 'CURRENT_TIMESTAMP',
  })
  assignedAt!: Date;

  @ManyToOne('AdminAccountEntity', 'adminRoleMappings')
  @JoinColumn([{ name: 'admin_id', referencedColumnName: 'adminId' }])
  admin!: 'AdminAccountEntity';

  @ManyToOne('GameEntity', 'adminRoleMappings', { onDelete: 'CASCADE' })
  @JoinColumn([{ name: 'game_id', referencedColumnName: 'gameId' }])
  game!: 'GameEntity';

  @ManyToOne('RoleEntity', 'adminRoleMappings')
  @JoinColumn([{ name: 'role_id', referencedColumnName: 'roleId' }])
  role!: 'RoleEntity';
}
