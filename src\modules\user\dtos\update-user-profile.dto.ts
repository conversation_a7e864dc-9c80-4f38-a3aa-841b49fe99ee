import {
  BooleanFieldOptional,
  DateFieldOptional,
  PhoneFieldOptional,
  StringFieldOptional,
} from '@decorators/field.decorators';

export class UpdateUserProfileDto {
  @StringFieldOptional()
  displayName?: string | null;

  @BooleanFieldOptional()
  gender?: boolean | null;

  @StringFieldOptional()
  avatarUrl?: string | null;

  @DateFieldOptional()
  dob?: string | null;

  @PhoneFieldOptional()
  phone?: string | null;

  @StringFieldOptional()
  address?: string | null;
}
