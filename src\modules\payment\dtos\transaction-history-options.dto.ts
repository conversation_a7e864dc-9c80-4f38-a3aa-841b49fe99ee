import { PageOptionsDto } from '@common/dto/page-options.dto';
import {
  DateFieldOptional,
  StringFieldOptional,
} from '@decorators/field.decorators';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

export class TransactionHistoryOptionsDto extends PageOptionsDto {
//   @StringFieldOptional()
//   @Expose({ name: 'filter_status' })
//   filterStatus?: string;

//   @DateFieldOptional()
//   @Expose({ name: 'date_from' })
//   dateFrom?: Date;

//   @DateFieldOptional()
//   @Expose({ name: 'date_to' })
//   dateTo?: Date;

}
