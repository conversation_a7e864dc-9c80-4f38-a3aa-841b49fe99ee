import { AbstractDto } from '@common/dto/abstract.dto';
import { UserAccountStatus, UserAccountType } from '@constants/user';
import {
  DateField,
  DateFieldOptional,
  EmailFieldOptional,
  EnumField,
  NumberField,
  StringFieldOptional,
} from '@decorators/field.decorators';
import { Exclude, Expose } from 'class-transformer';

import type { UserAccountEntity } from '../user-account.entity';

export class UserAccountDto extends AbstractDto {
  @NumberField()
  @Expose({ name: 'user_id' })
  userId!: number;

  @StringFieldOptional({ nullable: true })
  @Expose({ name: 'username' })
  username!: string | null;

  @EmailFieldOptional({ nullable: true })
  @Expose({ name: 'email' })
  email?: string | null;

  @StringFieldOptional()
  @Exclude()
  passwordHash?: string | null;

  @StringFieldOptional()
  @Exclude()
  refreshToken?: string | null;

  @DateFieldOptional()
  @Exclude()
  refreshTokenExpiresAt?: Date | null;

  @EnumField(() => UserAccountStatus)
  @Expose({ name: 'status' })
  status: UserAccountStatus;

  @EnumField(() => UserAccountType)
  @Expose({ name: 'account_type' })
  accountType: UserAccountType;

  @StringFieldOptional()
  @Expose({ name: 'social_uid' })
  socialUid?: string | null;

  @DateFieldOptional()
  @Expose({ name: 'linked_at' })
  linkedAt?: Date | null;

  @StringFieldOptional()
  @Expose({ name: 'social_access_token' })
  socialAccessToken?: string | null;

  @StringFieldOptional()
  @Expose({ name: 'social_refresh_token' })
  socialRefreshToken?: string | null;

  @NumberField()
  @Expose({ name: 'user_balance' })
  userBalance!: number;

  @DateField()
  @Expose({ name: 'created_at' })
  declare createdAt: Date;

  @DateField()
  @Expose({ name: 'updated_at' })
  declare updatedAt: Date;

  @DateFieldOptional()
  @Expose({ name: 'last_login_at' })
  declare lastLoginAt?: Date | null;

  @StringFieldOptional()
  @Expose({ name: 'created_at_ip' })
  createdAtIp?: string | null;

  @StringFieldOptional()
  @Expose({ name: 'last_login_at_ip' })
  lastLoginAtIp?: string | null;

  constructor(userAccount: UserAccountEntity) {
    super(userAccount);
    this.userId = userAccount.userId;
    this.username = userAccount.username ?? null;
    this.email = userAccount.email;
    this.passwordHash = userAccount.passwordHash;
    this.refreshToken = userAccount.refreshToken;
    this.refreshTokenExpiresAt = userAccount.refreshTokenExpiresAt;
    this.status = userAccount.status as UserAccountStatus;
    this.accountType = userAccount.accountType as UserAccountType;
    this.socialUid = userAccount.socialUid;
    this.linkedAt = userAccount.linkedAt;
    this.socialAccessToken = userAccount.socialAccessToken;
    this.socialRefreshToken = userAccount.socialRefreshToken;
    this.userBalance = userAccount.userBalance;
    this.createdAt = userAccount.createdAt;
    this.updatedAt = userAccount.updatedAt;
    this.lastLoginAt = userAccount.lastLoginAt;
    this.createdAtIp = userAccount.createdAtIp;
    this.lastLoginAtIp = userAccount.lastLoginAtIp;
  }
}
