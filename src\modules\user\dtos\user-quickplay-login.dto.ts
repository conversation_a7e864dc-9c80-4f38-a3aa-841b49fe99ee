import { PlatformType } from '@constants/platform';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  StringField,
  StringFieldOptional,
} from '@decorators/field.decorators';
import { Expose } from 'class-transformer';

export class UserQuickplayLoginDto {
  @StringFieldOptional()
  username?: string | null;

  // android: ANDROID_ID (Secure.getString(contentResolver, Secure.ANDROID_ID))
  // ios:     IDFV       (UIDevice.current.identifierForVendor?.uuidString)

  @StringField()
  @Expose({ name: 'unique_id' })
  uniqueId!: string;

  @StringField()
  @Expose({ name: 'device_id' })
  deviceId!: string;

  @EnumField(() => PlatformType)
  platform!: PlatformType;

  constructor(partial: Partial<UserQuickplayLoginDto>) {
    Object.assign(this, partial);
  }
}
