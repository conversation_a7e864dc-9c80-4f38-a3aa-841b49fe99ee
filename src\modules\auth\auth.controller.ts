import { getIp } from '@common/utils.ts';
import { RoleType } from '@constants/role-type';
import { AuthUser } from '@decorators/auth-user.decorator';
import { Auth } from '@decorators/http.decorators';
import { UserAccountDto } from '@modules/user/dtos/user-account.dto';
import { UserService } from '@modules/user/user.service';
import type { UserAccountEntity } from '@modules/user/user-account.entity';
import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import type { Request, Response } from 'express';

import { AuthService } from './auth.service.ts';
import { LoginPayloadDto } from './dto/login-payload.dto.ts';
import { RefreshTokenDto } from './dto/refresh-token.dto.ts';
import type { SocialInfoDto } from './dto/social-info.dto.ts';
import { TokenPayloadDto } from './dto/token-payload.dto.ts';
import { UserLoginDto } from './dto/user-login.dto.ts';
import { UserRegisterDto } from './dto/user-register.dto.ts';

@Controller('auth')
@ApiTags('auth')
export class AuthController {
  constructor(
    private userService: UserService,
    private authService: AuthService,
  ) {}

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: LoginPayloadDto,
    description: 'User info with access token',
  })
  async userLogin(
    @Body() userLoginDto: UserLoginDto,
    @Req() request: Request,
  ): Promise<LoginPayloadDto> {
    const userAccountEntity = await this.authService.validateUser(userLoginDto);

    const token = await this.authService.createToken({
      userId: userAccountEntity.userId,
      role: RoleType.USER,
    });
    console.log(
      `User ${userAccountEntity.username} logged in with access token: ${token.token}`,
    );

    this.userService.updateLastLoginInfo(
      userAccountEntity.userId,
      getIp(request),
    );
    this.userService.updateRefreshToken(
      userAccountEntity.userId,
      token.refreshToken,
    );

    return new LoginPayloadDto(userAccountEntity.toDto(), token);
  }

  @Post('register')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({
    type: UserAccountDto,
    description: 'Successfully Registered',
  })
  //   @ApiFile({ name: 'avatar' })
  async userRegister(
    @Body() userRegisterDto: UserRegisterDto,
    @Req() request: Request,
    // @UploadedFile() file?: Reference<IFile>,
  ): Promise<UserAccountDto> {
    if (!userRegisterDto.username) {
      throw new Error('Username must be provided');
    }

    const createdUser = await this.userService.createUser(
      userRegisterDto,
      getIp(request),
    );

    return createdUser.toDto();
  }

  @Post('refresh-token')
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ type: TokenPayloadDto, description: 'Get new access token' })
  async userRefreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
    // @Req() request: Request,
  ): Promise<TokenPayloadDto> {
    if (!refreshTokenDto.refreshToken) {
      throw new Error('Refresh token must be provided');
    }

    const userAccountEntity = await this.authService.getUserFromRefreshToken(
      refreshTokenDto.refreshToken,
    );

    if (!userAccountEntity) {
      throw new Error('Invalid refresh token');
    }

    const token = await this.authService.createToken({
      userId: userAccountEntity.userId,
      role: RoleType.USER,
    });

    // this.userService.updateLastLoginInfo(userAccountEntity.userId, this.getIp(request));
    this.userService.updateRefreshToken(
      userAccountEntity.userId,
      token.refreshToken,
    );

    return token;
  }

  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @Auth([RoleType.USER])
  @ApiOkResponse({
    type: UserAccountDto,
    description: 'Successfully logged out',
  })
  async userLogout(
    @AuthUser() user: UserAccountEntity,
    // @Req() request: Request,
  ): Promise<{ message: string }> {
    await this.userService.updateRefreshToken(user.userId, null);
    // await this.userService.updateLastLoginInfo(user.userId, this.getIp(request));
    console.log(`User ${user.username} logged out`);

    return { message: 'Successfully logged out' };
  }

  @Get('oauth2/google')
  @UseGuards(AuthGuard('google'))
  async googleLogin() {
    // This route triggers the Google OAuth2 flow
    console.log('Redirecting to Google for OAuth2 login');
  }

  @Get('oauth2/google/callback')
  @UseGuards(AuthGuard('google'))
  async googleCallback(@Req() req: Request, @Res() res: Response) {
    // Here, you have access to req.user (from validate)
    // You can create a JWT and redirect to frontend
    console.log('User from OAuth:', req.user);
    const response = await this.authService.loginWithOAuth(
      req.user as SocialInfoDto,
      getIp(req),
    );

    if (!response.token) {
      // Handle case where user is not active
      res.redirect(
        `${process.env.FRONTEND_URL}/auth/callback?status=${response.status}`,
      );

      return;
    }

    // return res.redirect(`http://your-frontend.com/auth/callback?token=${jwt}`);
    res.redirect(
      `${process.env.FRONTEND_URL}/auth/callback?token=${response.token}`,
    );
  }

  @Get('oauth2/facebook')
  @UseGuards(AuthGuard('facebook'))
  async facebookLogin() {
    // Triggers Facebook OAuth2 login redirect
    console.log('Redirecting to Facebook for OAuth2 login');
  }

  @Get('oauth2/facebook/callback')
  @UseGuards(AuthGuard('facebook'))
  async facebookCallback(@Req() req: Request, @Res() res: Response) {
    // Handle redirect, issue JWT or redirect
    const response = await this.authService.loginWithOAuth(
      req.user as SocialInfoDto,
      getIp(req),
    );

    if (!response.token) {
      // Handle case where user is not active
      res.redirect(
        `${process.env.FRONTEND_URL}/auth/callback?status=${response.status}`,
      );

      return;
    }

    // return res.redirect(`http://your-frontend.com/auth/callback?token=${jwt}`);
    res.redirect(
      `${process.env.FRONTEND_URL}/auth/callback?token=${response.token}`,
    );
  }
}
