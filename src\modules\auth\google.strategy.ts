import { UserAccountType } from '@constants/user';
import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import type { VerifyCallback } from 'passport-google-oauth20';
import { Strategy } from 'passport-google-oauth20';

import { SocialInfoDto } from './dto/social-info.dto.ts';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor() {
    const envVars = [
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
    ];
    console.log('Checking environment variables:', envVars);

    super({
      clientID: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      callbackURL: `${process.env.BACKEND_URL}/auth/oauth2/google/callback`,
      scope: ['email', 'profile'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    console.log('Google strategy validate called with profile:', profile);
    const { name, emails, photos } = profile;
    const socialInfo = new SocialInfoDto({
      socialUid: profile.id,
      name: profile.displayName || `${name.givenName} ${name.familyName}`,
      email: emails[0].value,
      avatarUrl: photos[0].value,
      provider: UserAccountType.GOOGLE,
      accessToken,
      refreshToken,
    });
    console.log('Google user profile:', socialInfo);
    done(null, socialInfo);
  }
}
