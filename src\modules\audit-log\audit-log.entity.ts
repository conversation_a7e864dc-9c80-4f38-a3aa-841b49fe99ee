import { AbstractEntity } from '@common/abstract.entity';
import { UseDto } from '@decorators/use-dto.decorator';
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { AuditLogDto } from './dtos/audit-log.dto.ts';

@Index('idx_audit_action', ['actionType'], {})
@Index('audit_log_pkey', ['logId'], { unique: true })
@Index('idx_audit_user', ['userId'], {})
@Entity('audit_log', { schema: 'public' })
@UseDto(AuditLogDto)
export class AuditLogEntity extends AbstractEntity<AuditLogDto> {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'log_id' })
  logId!: number;

  @Column('bigint', { name: 'user_id', nullable: true })
  userId?: number | null;

  @Column('bigint', { name: 'admin_id', nullable: true })
  adminId?: number | null;

  @Column('character varying', { name: 'action_type', length: 50 })
  actionType!: string;

  @Column('character varying', {
    name: 'description',
    nullable: true,
    length: 500,
  })
  description!: string | null;

  @Column('character varying', {
    name: 'ip_address',
    nullable: true,
    length: 45,
  })
  ipAddress!: string | null;

  @Column('timestamp without time zone', {
    name: 'created_at',
    default: () => 'CURRENT_TIMESTAMP',
  })
  declare createdAt: Date;

  @ManyToOne('AdminAccountEntity', 'auditLogs')
  @JoinColumn([{ name: 'admin_id', referencedColumnName: 'adminId' }])
  admin?: 'AdminAccountEntity';

  @ManyToOne('UserAccountEntity', 'auditLogs')
  @JoinColumn([{ name: 'user_id', referencedColumnName: 'userId' }])
  user?: 'UserAccountEntity';
}
