import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { CreateUserProfileHandler } from './commands/create-user-profile.command.ts';
// import { CreateSettingsHandler } from './commands/create-settings.command.ts';
import { UserController } from './user.controller.ts';
import { UserService } from './user.service.ts';
import { UserAccountEntity } from './user-account.entity.ts';
import { UserProfileEntity } from './user-profile.entity.ts';
import { PaymentTransactionEntity } from '@modules/payment/payment-transaction.entity.ts';

const handlers = [CreateUserProfileHandler];

@Module({
  imports: [TypeOrmModule.forFeature([UserAccountEntity, UserProfileEntity, PaymentTransactionEntity])],
  controllers: [UserController],
  exports: [UserService],
  providers: [UserService, ...handlers],
})
export class UserModule {}
