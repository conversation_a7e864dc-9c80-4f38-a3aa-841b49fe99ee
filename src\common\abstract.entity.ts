import { LanguageCode } from '@constants/language-code';
import {
  Column,
  CreateDateColumn,
  //   PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import type {
  AbstractDto,
  AbstractTranslationDto,
} from './dto/abstract.dto.ts';

/**
 * Abstract Entity
 * <AUTHOR> <<EMAIL>>
 *
 * @description This class is an abstract class for all entities.
 * It's experimental and recommended using it only in microservice architecture,
 * otherwise just delete and use your own entity.
 */
export abstract class AbstractEntity<
  DTO extends AbstractDto = AbstractDto,
  O = never,
> {
  //   @PrimaryGeneratedColumn('uuid')
  //   id!: Uuid;

  @CreateDateColumn({
    type: 'timestamp',
  })
  createdAt!: Date;

  @UpdateDateColumn({
    type: 'timestamp',
  })
  updatedAt!: Date;

  translations?: AbstractTranslationEntity[];

  toDto(options?: O): DTO {
    const dtoClass = Object.getPrototypeOf(this).dtoClass;

    if (!dtoClass) {
      throw new Error(
        `You need to use @UseDto on class (${this.constructor.name}) be able to call toDto function`,
      );
    }

    return new dtoClass(this, options);
  }
}

export class AbstractTranslationEntity<
  DTO extends AbstractTranslationDto = AbstractTranslationDto,
  O = never,
> extends AbstractEntity<DTO, O> {
  @Column({ type: 'enum', enum: LanguageCode })
  languageCode!: LanguageCode;
}
