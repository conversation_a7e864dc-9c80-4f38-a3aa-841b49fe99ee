import {
  BooleanFieldOptional,
  DateFieldOptional,
  StringFieldOptional,
} from '@decorators/field.decorators';

export class CreateUserProfileDto {
  @StringFieldOptional()
  displayName?: string;

  @BooleanFieldOptional()
  gender?: boolean;

  @StringFieldOptional()
  avatarUrl?: string;

  @DateFieldOptional()
  dob?: string;

  // @PhoneFieldOptional()
  @StringFieldOptional()
  phone?: string;

  @StringFieldOptional()
  address?: string;
}
