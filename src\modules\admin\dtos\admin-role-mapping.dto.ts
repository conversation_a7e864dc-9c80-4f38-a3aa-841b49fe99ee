import { AbstractDto } from '@common/dto/abstract.dto';
import {
  DateField,
  NumberField,
  NumberFieldOptional,
  StringField,
  StringFieldOptional,
} from '@decorators/field.decorators';

import type { AdminRoleMappingEntity } from '../admin-role-mapping.entity';

export class AdminRoleMappingDto extends AbstractDto {
  @StringField()
  armId?: string;

  @StringFieldOptional()
  adminId?: string | null;

  @NumberField()
  roleId?: number;

  @NumberFieldOptional()
  gameId?: number | null;

  @DateField()
  assignedAt?: Date;

  @DateField()
  createdAt: Date;

  @DateField()
  updatedAt: Date;

  constructor(adminRoleMapping: AdminRoleMappingEntity) {
    super(adminRoleMapping);
    this.armId = adminRoleMapping.armId;
    this.adminId = adminRoleMapping.adminId;
    this.roleId = adminRoleMapping.roleId;
    this.gameId = adminRoleMapping.gameId;
    this.assignedAt = adminRoleMapping.assignedAt;
    this.createdAt = adminRoleMapping.createdAt;
    this.updatedAt = adminRoleMapping.updatedAt;
  }
}
