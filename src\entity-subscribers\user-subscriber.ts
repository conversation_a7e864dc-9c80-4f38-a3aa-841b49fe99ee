import { generateHash } from '@common/utils';
import { UserAccountEntity } from '@modules/user/user-account.entity';
import type {
  EntitySubscriberInterface,
  InsertEvent,
  UpdateEvent,
} from 'typeorm';
import { EventSubscriber } from 'typeorm';

@EventSubscriber()
export class UserSubscriber
  implements EntitySubscriberInterface<UserAccountEntity>
{
  listenTo(): typeof UserAccountEntity {
    return UserAccountEntity;
  }

  beforeInsert(event: InsertEvent<UserAccountEntity>): void {
    console.log('beforeInsert', event.entity);

    if (event.entity.passwordHash) {
      event.entity.passwordHash = generateHash(event.entity.passwordHash);
    }
  }

  beforeUpdate(event: UpdateEvent<UserAccountEntity>): void {
    console.log('beforeUpdate', event.entity, event.databaseEntity);
    // FIXME check event.databaseEntity.password
    const entity = event.entity as UserAccountEntity;

    if (
      entity.passwordHash &&
      entity.passwordHash !== event.databaseEntity.passwordHash
    ) {
      entity.passwordHash = generateHash(entity.passwordHash);
    }
  }
}
